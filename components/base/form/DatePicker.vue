<template>
	<VueDatePicker v-model="dateValue" @update:model-value="handleDate" :ui="{input: props.error ? 'field_error_input' : ''}" :clearable="false" :auto-apply="true" :enable-time-picker="false" :locale="lang.get('locale')" :format="format" hide-input-icon autocomplete="off" v-bind="$attrs" />
</template>

<script setup>
	import VueDatePicker from '@vuepic/vue-datepicker';
	import '@vuepic/vue-datepicker/dist/main.css';

	// Props for custom classes
	const props = defineProps({
		error: String,
	});

	const model = defineModel();
	const lang = useLang();
	const dateValue = ref(model.value ? parseDate(model.value) : null);

	// Convert initial value from DD.MM.YYYY format or Unix timestamp to Date object
	function parseDate(date) {
		if (!date) return null;

		// Check if the input is a Unix timestamp (numeric string or number) and convert it to Date object
		if (/^\d+$/.test(date.toString())) {
			return new Date(parseInt(date) * 1000);
		}

		// Handle DD.MM.YYYY format
		if (date.includes('.')) {
			const [day, month, year] = date.split('.');
			return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
		}

		// Fallback: try to parse as a regular date string
		return new Date(date);
	}

	// Format input value to DD.MM.YYYY format
	const format = date => {
		const day = date.getDate().toString().padStart(2, '0');
		const month = (date.getMonth() + 1).toString().padStart(2, '0');
		const year = date.getFullYear();
		return `${day}.${month}.${year}.`;
	};

	// Update model value (send to parent) when date changes
	function handleDate(newDate) {
		model.value = newDate ? format(newDate) : '';
	}
</script>
